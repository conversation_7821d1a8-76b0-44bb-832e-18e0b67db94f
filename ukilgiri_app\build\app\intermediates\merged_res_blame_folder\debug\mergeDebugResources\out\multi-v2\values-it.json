{"logs": [{"outputFile": "com.example.ukilgiri_app-mergeDebugResources-44:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f9271a16790b10f6f400891616470b9a\\transformed\\jetified-play-services-base-18.1.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3546,3651,3802,3927,4035,4193,4321,4441,4681,4838,4945,5099,5226,5382,5563,5630,5691", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "3646,3797,3922,4030,4188,4316,4436,4540,4833,4940,5094,5221,5377,5558,5625,5686,5763"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\57ca061d6f83c02836e249974e533058\\transformed\\browser-1.8.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5837,6024,6122,6232", "endColumns": "99,97,109,102", "endOffsets": "5932,6117,6227,6330"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\584a7d9af8fd0860fd9671dd1719eb16\\transformed\\appcompat-1.1.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,884,976,1069,1162,1256,1358,1452,1549,1644,1736,1828,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,75,91,92,92,93,101,93,96,94,91,91,79,105,106,97,103,105,106,162,99,80", "endOffsets": "205,308,417,501,606,725,803,879,971,1064,1157,1251,1353,1447,1544,1639,1731,1823,1903,2009,2116,2214,2318,2424,2531,2694,2794,2875"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,884,976,1069,1162,1256,1358,1452,1549,1644,1736,1828,1908,2014,2121,2219,2323,2429,2536,2699,6550", "endColumns": "104,102,108,83,104,118,77,75,91,92,92,93,101,93,96,94,91,91,79,105,106,97,103,105,106,162,99,80", "endOffsets": "205,308,417,501,606,725,803,879,971,1064,1157,1251,1353,1447,1544,1639,1731,1823,1903,2009,2116,2214,2318,2424,2531,2694,2794,6626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\058a70fc25a41fe25172013c6f3c7c95\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4545", "endColumns": "135", "endOffsets": "4676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3e7e3c83388001da39a5d44ececd8e9e\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2799,2897,2999,3098,3200,3309,3416,6631", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "2892,2994,3093,3195,3304,3411,3541,6727"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0aec81ac1045261b57de152fa0160835\\transformed\\preference-1.2.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,261,341,476,645,725", "endColumns": "68,86,79,134,168,79,75", "endOffsets": "169,256,336,471,640,720,796"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5768,5937,6335,6415,6732,6901,6981", "endColumns": "68,86,79,134,168,79,75", "endOffsets": "5832,6019,6410,6545,6896,6976,7052"}}]}]}