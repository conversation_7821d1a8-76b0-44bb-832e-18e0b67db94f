{"logs": [{"outputFile": "com.example.ukilgiri_app-mergeDebugResources-44:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\57ca061d6f83c02836e249974e533058\\transformed\\browser-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5984,6188,6287,6399", "endColumns": "115,98,111,102", "endOffsets": "6095,6282,6394,6497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\584a7d9af8fd0860fd9671dd1719eb16\\transformed\\appcompat-1.1.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,740,825,906,998,1091,1188,1282,1382,1476,1572,1667,1759,1851,1935,2042,2153,2255,2363,2471,2578,2749,2848", "endColumns": "107,105,106,88,100,123,84,80,91,92,96,93,99,93,95,94,91,91,83,106,110,101,107,107,106,170,98,84", "endOffsets": "208,314,421,510,611,735,820,901,993,1086,1183,1277,1377,1471,1567,1662,1754,1846,1930,2037,2148,2250,2358,2466,2573,2744,2843,2928"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,426,515,616,740,825,906,998,1091,1188,1282,1382,1476,1572,1667,1759,1851,1935,2042,2153,2255,2363,2471,2578,2749,6732", "endColumns": "107,105,106,88,100,123,84,80,91,92,96,93,99,93,95,94,91,91,83,106,110,101,107,107,106,170,98,84", "endOffsets": "208,314,421,510,611,735,820,901,993,1086,1183,1277,1377,1471,1567,1662,1754,1846,1930,2037,2148,2250,2358,2466,2573,2744,2843,6812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f9271a16790b10f6f400891616470b9a\\transformed\\jetified-play-services-base-18.1.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3580,3685,3848,3976,4084,4252,4380,4502,4756,4944,5052,5222,5353,5512,5690,5758,5827", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "3680,3843,3971,4079,4247,4375,4497,4606,4939,5047,5217,5348,5507,5685,5753,5822,5909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3e7e3c83388001da39a5d44ececd8e9e\\transformed\\core-1.13.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2848,2945,3047,3146,3246,3353,3459,6817", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "2940,3042,3141,3241,3348,3454,3575,6913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\058a70fc25a41fe25172013c6f3c7c95\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4611", "endColumns": "144", "endOffsets": "4751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0aec81ac1045261b57de152fa0160835\\transformed\\preference-1.2.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,342,493,662,749", "endColumns": "69,87,78,150,168,86,80", "endOffsets": "170,258,337,488,657,744,825"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5914,6100,6502,6581,6918,7087,7174", "endColumns": "69,87,78,150,168,86,80", "endOffsets": "5979,6183,6576,6727,7082,7169,7250"}}]}]}