{"logs": [{"outputFile": "com.example.ukilgiri_app-mergeDebugResources-44:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\584a7d9af8fd0860fd9671dd1719eb16\\transformed\\appcompat-1.1.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,914,1005,1097,1192,1286,1387,1480,1575,1670,1761,1852,1934,2048,2150,2247,2362,2465,2580,2742,2845", "endColumns": "116,111,110,89,104,118,77,76,90,91,94,93,100,92,94,94,90,90,81,113,101,96,114,102,114,161,102,79", "endOffsets": "217,329,440,530,635,754,832,909,1000,1092,1187,1281,1382,1475,1570,1665,1756,1847,1929,2043,2145,2242,2357,2460,2575,2737,2840,2920"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,914,1005,1097,1192,1286,1387,1480,1575,1670,1761,1852,1934,2048,2150,2247,2362,2465,2580,2742,6621", "endColumns": "116,111,110,89,104,118,77,76,90,91,94,93,100,92,94,94,90,90,81,113,101,96,114,102,114,161,102,79", "endOffsets": "217,329,440,530,635,754,832,909,1000,1092,1187,1281,1382,1475,1570,1665,1756,1847,1929,2043,2145,2242,2357,2460,2575,2737,2840,6696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f9271a16790b10f6f400891616470b9a\\transformed\\jetified-play-services-base-18.1.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,449,575,686,819,940,1041,1137,1282,1390,1539,1667,1814,1973,2033,2099", "endColumns": "105,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,79", "endOffsets": "298,448,574,685,818,939,1040,1136,1281,1389,1538,1666,1813,1972,2032,2098,2178"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3595,3705,3859,3989,4104,4241,4366,4471,4709,4858,4970,5123,5255,5406,5569,5633,5703", "endColumns": "109,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,83", "endOffsets": "3700,3854,3984,4099,4236,4361,4466,4566,4853,4965,5118,5250,5401,5564,5628,5698,5782"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\058a70fc25a41fe25172013c6f3c7c95\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4571", "endColumns": "137", "endOffsets": "4704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\57ca061d6f83c02836e249974e533058\\transformed\\browser-1.8.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,385", "endColumns": "110,107,110,106", "endOffsets": "161,269,380,487"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5860,6070,6178,6289", "endColumns": "110,107,110,106", "endOffsets": "5966,6173,6284,6391"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3e7e3c83388001da39a5d44ececd8e9e\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "29,30,31,32,33,34,35,63", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2845,2947,3055,3157,3258,3364,3471,6701", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "2942,3050,3152,3253,3359,3466,3590,6797"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0aec81ac1045261b57de152fa0160835\\transformed\\preference-1.2.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,277,356,502,671,758", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "173,272,351,497,666,753,837"}, "to": {"startLines": "54,56,60,61,64,65,66", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5787,5971,6396,6475,6802,6971,7058", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "5855,6065,6470,6616,6966,7053,7137"}}]}]}